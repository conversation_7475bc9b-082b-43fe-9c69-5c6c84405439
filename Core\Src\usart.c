/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.c
  * @brief   This file provides code for the configuration
  *          of the USART instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "usart.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

UART_HandleTypeDef huart2;

/* USART2 init function */

void MX_USART2_UART_Init(void)
{

  /* USER CODE BEGIN USART2_Init 0 */

  /* USER CODE END USART2_Init 0 */

  /* USER CODE BEGIN USART2_Init 1 */

  /* USER CODE END USART2_Init 1 */
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 115200;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART2_Init 2 */

  /* USER CODE END USART2_Init 2 */

}

void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspInit 0 */

  /* USER CODE END USART2_MspInit 0 */
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /* USER CODE BEGIN USART2_MspInit 1 */
    /* USART2 interrupt Init */
    HAL_NVIC_SetPriority(USART2_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(USART2_IRQn);
  /* USER CODE END USART2_MspInit 1 */
  }
}

void HAL_UART_MspDeInit(UART_HandleTypeDef* uartHandle)
{

  if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspDeInit 0 */

  /* USER CODE END USART2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART2_CLK_DISABLE();

    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_2|GPIO_PIN_3);

  /* USER CODE BEGIN USART2_MspDeInit 1 */

  /* USER CODE END USART2_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
// 外部变量声明
extern uint8_t USART_RX_BUF[200];
extern uint16_t USART_RX_STA;

// 串口接收完成回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    static uint16_t rx_count = 0;

    if(huart->Instance == USART2)
    {
        // 简化的接收逻辑
        if(rx_count == 0) // 接收第一个字节
        {
            // 根据第一个字节判断需要接收多少字节
            if(USART_RX_BUF[0] == '3') // 命令3需要5个字节
            {
                rx_count = 1;
                HAL_UART_Receive_IT(&huart2, &USART_RX_BUF[1], 1);
            }
            else if(USART_RX_BUF[0] == 0xfd) // 握手信号需要4个字节
            {
                rx_count = 1;
                HAL_UART_Receive_IT(&huart2, &USART_RX_BUF[1], 1);
            }
            else // 其他命令只需要1个字节
            {
                USART_RX_STA = 1 | 0x8000; // 标记接收完成
                rx_count = 0;
                HAL_UART_Receive_IT(&huart2, USART_RX_BUF, 1); // 重新开始接收
            }
        }
        else // 接收后续字节
        {
            rx_count++;

            // 检查是否接收完成
            if((USART_RX_BUF[0] == '3' && rx_count >= 5) ||
               (USART_RX_BUF[0] == 0xfd && rx_count >= 4))
            {
                USART_RX_STA = rx_count | 0x8000; // 标记接收完成
                rx_count = 0;
                HAL_UART_Receive_IT(&huart2, USART_RX_BUF, 1); // 重新开始接收
            }
            else if(rx_count < 199) // 继续接收
            {
                HAL_UART_Receive_IT(&huart2, &USART_RX_BUF[rx_count], 1);
            }
            else // 接收错误，重新开始
            {
                rx_count = 0;
                USART_RX_STA = 0;
                HAL_UART_Receive_IT(&huart2, USART_RX_BUF, 1);
            }
        }
    }
}
/* USER CODE END 1 */
