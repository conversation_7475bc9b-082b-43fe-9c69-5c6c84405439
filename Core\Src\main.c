/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "ad9959.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// 串口接收相关变量
uint8_t USART_RX_BUF[200]; // 接收缓冲区
uint16_t USART_RX_STA = 0;  // 接收状态标记

// HMI相关变量
uint32_t combined = 0;      // 频率值
float test_float = 0.0f;    // 浮点测试值
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
// HMI通信函数声明
void HMI_send_string(char* name, char* showdata);
void HMI_send_number(char* name, int num);
void HMI_send_float(char* name, float num);
void HMI_Wave(char* name, int ch, int val);
void HMI_Wave_Fast(char* name, int ch, int count, int* show_data);
void HMI_Wave_Clear(char* name, int ch);

// 串口重定向函数
int fputc(int ch, FILE *f);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// printf重定向到串口2
int fputc(int ch, FILE *f)
{
    HAL_UART_Transmit(&huart2, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

// HMI通信函数实现
void HMI_send_string(char* name, char* showdata)
{
    printf("%s=\"%s\"\xff\xff\xff", name, showdata);
}

void HMI_send_number(char* name, int num)
{
    printf("%s=%d\xff\xff\xff", name, num);
}

void HMI_send_float(char* name, float num)
{
    printf("%s=%d\xff\xff\xff", name, (int)(num * 100));
}

void HMI_Wave(char* name, int ch, int val)
{
    printf("add %s,%d,%d\xff\xff\xff", name, ch, val);
}

void HMI_Wave_Fast(char* name, int ch, int count, int* show_data)
{
    int i;
    printf("addt %s,%d,%d\xff\xff\xff", name, ch, count);
    HAL_Delay(100);
    for (i = 0; i < count; i++)
        printf("%c", show_data[i]);
    printf("\xff\xff\xff");
}

void HMI_Wave_Clear(char* name, int ch)
{
    printf("cle %s,%d\xff\xff\xff", name, ch);
}
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART2_UART_Init();
  /* USER CODE BEGIN 2 */
	ad9959_init();
	ad9959_write_amplitude(AD9959_CHANNEL_0,1023);
	ad9959_write_amplitude(AD9959_CHANNEL_1,1023);

	ad9959_write_frequency(AD9959_CHANNEL_0,200000);
	ad9959_write_frequency(AD9959_CHANNEL_1,20000000);

	// 启用串口2中断接收
	HAL_UART_Receive_IT(&huart2, USART_RX_BUF, 1);
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // 处理串口接收到的数据
    if (USART_RX_STA & 0x8000) {
        uint8_t len = USART_RX_STA & 0x3fff; // 得到此次接收到的数据长度

        // 检查是否为握手信号
        if (USART_RX_BUF[0] == 0xfd && USART_RX_BUF[1] == 0xff
            && USART_RX_BUF[2] == 0xff && USART_RX_BUF[3] == 0xff) {
            memset(USART_RX_BUF, 0, 200); // 清空缓存区
            printf("OKK");
        }

        // 频率增加100
        if (USART_RX_BUF[0] == '1') // 0x31
        {
            combined = combined + 100;
            HMI_send_number("n0.val", combined);
            // 控制AD9959频率
            ad9959_write_frequency(AD9959_CHANNEL_0, combined);
        }

        // 频率减少100
        if (USART_RX_BUF[0] == '2') // 0x32
        {
            combined = combined - 100;
            HMI_send_number("n0.val", combined);
            // 控制AD9959频率
            ad9959_write_frequency(AD9959_CHANNEL_0, combined);
        }

        // 设置具体频率值
        if (USART_RX_BUF[0] == '3') // 0x33
        {
            // 拼接四个8位变量，更新为频率
            uint8_t num1 = USART_RX_BUF[1];
            uint8_t num2 = USART_RX_BUF[2];
            uint8_t num3 = USART_RX_BUF[3];
            uint8_t num4 = USART_RX_BUF[4];

            // 从低到高拼接成32位整数（num1为最低位，num4为最高位）
            combined = (uint32_t)num4 << 24 | (uint32_t)num3 << 16 |
                      (uint32_t)num2 << 8  | (uint32_t)num1;
            HMI_send_number("n0.val", combined);
            // 控制AD9959频率
            ad9959_write_frequency(AD9959_CHANNEL_0, combined);
        }

        // 浮点数增加
        if (USART_RX_BUF[0] == '4') // 0x34
        {
            test_float += 0.1f;
            HMI_send_float("x0.val", test_float);
        }

        // 浮点数减少
        if (USART_RX_BUF[0] == '5') // 0x35
        {
            test_float = test_float - 0.1f;
            HMI_send_float("x0.val", test_float);
        }

        // 清除波形
        if (USART_RX_BUF[0] == '6') // 0x36
        {
            HMI_Wave_Clear("s0.id", 0);
        }

        USART_RX_STA = 0; // 清除接收完成标志
    }
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 128;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
